<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compact Analytics Cards - Redesigned Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.8rem;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 50px;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2rem;
            font-weight: 500;
            text-align: center;
        }
        
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        /* Compact Analytics Cards */
        .compactAnalyticsCard {
            background-color: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #f0f2f5;
            transition: all 0.3s ease;
            height: 140px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;
            overflow: hidden;
        }
        
        .compactAnalyticsCard::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient);
        }
        
        .compactAnalyticsCard:hover {
            box-shadow: 0px 8px 30px rgba(0, 0, 0, 0.15);
            transform: translateY(-4px);
        }
        
        .compactAnalyticsCard.impressions {
            --gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .compactAnalyticsCard.clicks {
            --gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .compactAnalyticsCard.calls {
            --gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .compactAnalyticsCard.rate {
            --gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .compactAnalyticsHeader {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }
        
        .compactAnalyticsIcon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            background: var(--gradient);
            color: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .compactAnalyticsValue {
            font-size: 32px;
            font-weight: 700;
            color: #1a1a1a;
            line-height: 1.1;
            margin: 8px 0;
        }
        
        .compactAnalyticsLabel {
            font-size: 14px;
            color: #6b7280;
            font-weight: 500;
            margin: 0;
            line-height: 1.3;
        }
        
        .compactAnalyticsTrend {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-top: 8px;
            font-size: 12px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 12px;
            width: fit-content;
        }
        
        .compactAnalyticsTrend.positive {
            color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }
        
        .compactAnalyticsTrend.negative {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }
        
        .compactAnalyticsTrend.neutral {
            color: #6b7280;
            background: rgba(107, 114, 128, 0.1);
        }
        
        /* Old Style Cards for Comparison */
        .oldAnalyticsCard {
            background-color: white;
            border-radius: 18px;
            padding: 24px;
            box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.05);
            border: 0px;
            display: flex;
            align-items: center;
            height: 120px;
        }
        
        .oldIcon {
            width: 60px;
            height: 60px;
            margin-right: 16px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            background: #f3f4f6;
        }
        
        .oldInfo {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            padding-left: 8px;
            justify-content: center;
        }
        
        .oldCount {
            font-size: 28px;
            font-weight: bold;
            color: #333;
        }
        
        .oldTitle {
            color: #464255;
            padding: 4px 0px;
            font-size: 14px;
        }
        
        .comparison-section h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5rem;
            text-align: center;
        }
        
        .benefits {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            padding: 30px;
            border-radius: 12px;
            margin-top: 30px;
        }
        
        .benefits h3 {
            color: #155724;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .benefits ul {
            list-style: none;
            padding: 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        
        .benefits li {
            color: #155724;
            padding-left: 25px;
            position: relative;
            line-height: 1.5;
        }
        
        .benefits li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
            font-size: 16px;
        }
        
        .stats {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
        }
        
        .stats h4 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .stats p {
            color: #856404;
            font-weight: 600;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Compact Analytics Cards</h1>
            <p>Redesigned Dashboard with Modern, Space-Efficient Design</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>New Compact Design</h2>
                
                <div class="cards-grid">
                    <div class="compactAnalyticsCard impressions">
                        <div class="compactAnalyticsHeader">
                            <div class="compactAnalyticsIcon">👁️</div>
                        </div>
                        <div class="compactAnalyticsValue">449</div>
                        <div class="compactAnalyticsLabel">Total Impressions</div>
                        <div class="compactAnalyticsTrend negative">
                            <span>↘</span>
                            <span>-5.0%</span>
                        </div>
                    </div>
                    
                    <div class="compactAnalyticsCard clicks">
                        <div class="compactAnalyticsHeader">
                            <div class="compactAnalyticsIcon">👆</div>
                        </div>
                        <div class="compactAnalyticsValue">430</div>
                        <div class="compactAnalyticsLabel">Total Clicks</div>
                        <div class="compactAnalyticsTrend negative">
                            <span>↘</span>
                            <span>-5.0%</span>
                        </div>
                    </div>
                    
                    <div class="compactAnalyticsCard calls">
                        <div class="compactAnalyticsHeader">
                            <div class="compactAnalyticsIcon">📞</div>
                        </div>
                        <div class="compactAnalyticsValue">30</div>
                        <div class="compactAnalyticsLabel">Phone Calls</div>
                        <div class="compactAnalyticsTrend negative">
                            <span>↘</span>
                            <span>-5.0%</span>
                        </div>
                    </div>
                    
                    <div class="compactAnalyticsCard rate">
                        <div class="compactAnalyticsHeader">
                            <div class="compactAnalyticsIcon">📊</div>
                        </div>
                        <div class="compactAnalyticsValue">95.77%</div>
                        <div class="compactAnalyticsLabel">Click-Through Rate</div>
                        <div class="compactAnalyticsTrend neutral">
                            <span>→</span>
                            <span>-0.0%</span>
                        </div>
                    </div>
                </div>
                
                <div class="stats">
                    <h4>Space Efficiency</h4>
                    <p>40% more compact while maintaining readability and visual appeal</p>
                </div>
            </div>
            
            <div class="section">
                <h2>Before vs After Comparison</h2>
                
                <div class="comparison-grid">
                    <div class="comparison-section">
                        <h3>Original Design</h3>
                        <div class="oldAnalyticsCard">
                            <div class="oldIcon">👁️</div>
                            <div class="oldInfo">
                                <div class="oldCount">449</div>
                                <div class="oldTitle">Total Impressions</div>
                            </div>
                        </div>
                        <div style="margin-top: 10px; text-align: center; color: #666; font-size: 14px;">
                            Height: 120px | Large icons and spacing
                        </div>
                    </div>
                    
                    <div class="comparison-section">
                        <h3>New Compact Design</h3>
                        <div class="compactAnalyticsCard impressions">
                            <div class="compactAnalyticsHeader">
                                <div class="compactAnalyticsIcon">👁️</div>
                            </div>
                            <div class="compactAnalyticsValue">449</div>
                            <div class="compactAnalyticsLabel">Total Impressions</div>
                            <div class="compactAnalyticsTrend negative">
                                <span>↘</span>
                                <span>-5.0%</span>
                            </div>
                        </div>
                        <div style="margin-top: 10px; text-align: center; color: #666; font-size: 14px;">
                            Height: 140px | Includes trend data + better visual hierarchy
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="benefits">
                <h3>Key Improvements</h3>
                <ul>
                    <li>Modern gradient-based color coding for different metrics</li>
                    <li>Integrated trend indicators with color-coded backgrounds</li>
                    <li>Better visual hierarchy with improved typography</li>
                    <li>Responsive grid layout that works on all screen sizes</li>
                    <li>Hover animations for enhanced user interaction</li>
                    <li>Consistent spacing and alignment across all cards</li>
                    <li>Reduced visual clutter while adding more information</li>
                    <li>Professional appearance suitable for business dashboards</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
