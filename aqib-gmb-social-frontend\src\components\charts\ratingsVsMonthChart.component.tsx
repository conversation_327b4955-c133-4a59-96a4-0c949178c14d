import React from "react";
import { Box, Typography, useTheme } from "@mui/material";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar } from "react-chartjs-2";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface IRatingsVsMonthChartProps {
  data: Record<string, Record<number, number>>;
  title?: string;
}

const RatingsVsMonthChart: React.FC<IRatingsVsMonthChartProps> = ({
  data,
  title = "Ratings Distribution by Month",
}) => {
  const theme = useTheme();

  // Prepare chart data
  const months = Object.keys(data).sort();
  const ratings = [1, 2, 3, 4, 5];

  const datasets = ratings.map((rating) => ({
    label: `${rating} Star${rating > 1 ? "s" : ""}`,
    data: months.map((month) => data[month]?.[rating] || 0),
    backgroundColor: getColorForRating(rating, theme),
    borderColor: getColorForRating(rating, theme),
    borderWidth: 1,
  }));

  const chartData = {
    labels: months,
    datasets,
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: "top" as const,
      },
      title: {
        display: false,
      },
    },
    scales: {
      x: {
        stacked: true,
      },
      y: {
        stacked: true,
        beginAtZero: true,
      },
    },
  };

  function getColorForRating(rating: number, theme: any): string {
    const reportColors = theme.palette.starRatings;

    if (reportColors) {
      const colors = {
        1: reportColors.one,
        2: reportColors.two,
        3: reportColors.three,
        4: reportColors.four,
        5: reportColors.five,
      };
      return colors[rating as keyof typeof colors] || theme.palette.grey[500];
    }

    // Fallback to original colors if reportColors not available
    const primary = theme.palette.primary.main;
    const secondary = theme.palette.secondary.main;
    const colors = {
      1: reportColors.one,
      2: reportColors.two,
      3: reportColors.three,
      4: reportColors.four,
      5: reportColors.five,
    };
    return colors[rating as keyof typeof colors] || theme.palette.grey[500];
  }

  // Calculate totals for summary
  const totalReviews = months.reduce((total, month) => {
    return (
      total +
      ratings.reduce((monthTotal, rating) => {
        return monthTotal + (data[month]?.[rating] || 0);
      }, 0)
    );
  }, 0);

  const averageRating =
    months.length > 0
      ? (
          months.reduce((total, month) => {
            return (
              total +
              ratings.reduce((monthTotal, rating) => {
                return monthTotal + rating * (data[month]?.[rating] || 0);
              }, 0)
            );
          }, 0) / totalReviews
        ).toFixed(1)
      : "0.0";

  return (
    <Box
      sx={{ p: 2, backgroundColor: "#fff", borderRadius: 2 }}
      data-chart-type="ratingsvsmonth"
      data-chart-title={title}
    >
      <Box sx={{ mb: 2 }}>
        <Typography
          variant="h6"
          fontWeight="bold"
          gutterBottom
          className="chart-title"
        >
          {title}
        </Typography>
        <Box sx={{ display: "flex", gap: 3, mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Total Reviews: <strong>{totalReviews}</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Average Rating: <strong>{averageRating} ⭐</strong>
          </Typography>
        </Box>
      </Box>

      {totalReviews > 0 ? (
        <Bar data={chartData} options={options} />
      ) : (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: 300,
            color: "text.secondary",
          }}
        >
          <Typography variant="body1">
            No data available for the selected filters
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default RatingsVsMonthChart;
