import React, { useState } from "react";
import {
  Icon<PERSON><PERSON>on,
  Tooltip,
  CircularProgress,
  Box,
  Button,
  Chip,
} from "@mui/material";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";
import GetAppIcon from "@mui/icons-material/GetApp";
import {
  PDFExportService,
  FilterCriteria,
  ChartElement,
  SummaryData,
} from "../../services/pdfExport.service";

interface PDFExportButtonProps {
  reportTitle: string;
  filterCriteria: FilterCriteria;
  summaryData?: SummaryData;
  containerSelector?: string;
  chartElements?: ChartElement[];
  filename?: string;
  orientation?: "portrait" | "landscape";
  disabled?: boolean;
  size?: "small" | "medium" | "large";
  variant?: "icon" | "button" | "compact" | "chip";
  color?:
    | "primary"
    | "secondary"
    | "inherit"
    | "success"
    | "error"
    | "info"
    | "warning";
  tooltipText?: string;
  buttonText?: string;
  onExportStart?: () => void;
  onExportComplete?: () => void;
  onExportError?: (error: Error) => void;
}

const PDFExportButton: React.FC<PDFExportButtonProps> = ({
  reportTitle,
  filterCriteria,
  summaryData,
  containerSelector = ".analytics-container",
  chartElements,
  filename,
  orientation = "portrait",
  disabled = false,
  size = "medium",
  variant = "icon",
  color = "primary",
  tooltipText = "Export report as PDF",
  buttonText = "Export PDF",
  onExportStart,
  onExportComplete,
  onExportError,
}) => {
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    if (isExporting || disabled) return;

    try {
      setIsExporting(true);
      onExportStart?.();

      let chartsToExport: ChartElement[] = [];

      if (chartElements) {
        // Use provided chart elements
        chartsToExport = chartElements;
      } else {
        // Auto-capture charts from container
        chartsToExport = await PDFExportService.captureChartsFromContainer(
          containerSelector
        );
      }

      if (chartsToExport.length === 0) {
        throw new Error(
          "No charts found to export. Please ensure charts are loaded and try again."
        );
      }

      // Export to PDF
      await PDFExportService.exportReportToPDF({
        reportTitle,
        filterCriteria,
        summaryData,
        chartElements: chartsToExport,
        filename,
        orientation,
      });

      onExportComplete?.();
    } catch (error) {
      console.error("Error exporting PDF:", error);
      const errorMessage =
        error instanceof Error ? error : new Error("Unknown error occurred");
      onExportError?.(errorMessage);
    } finally {
      setIsExporting(false);
    }
  };

  const isButtonDisabled = disabled || isExporting;

  // Chip variant - very compact
  if (variant === "chip") {
    return (
      <Tooltip title={isButtonDisabled ? "Export not available" : tooltipText}>
        <Chip
          icon={
            isExporting ? (
              <CircularProgress
                size={16}
                color="inherit"
                sx={{ color: "white" }}
              />
            ) : (
              <GetAppIcon sx={{ fontSize: "16px !important" }} />
            )
          }
          label="PDF"
          onClick={handleExport}
          disabled={isButtonDisabled}
          color={color === "inherit" ? "default" : color}
          variant="filled"
          size={size === "large" ? "medium" : "small"}
          sx={{
            backgroundColor: isButtonDisabled
              ? "rgba(0, 0, 0, 0.12)"
              : color === "primary"
              ? "var(--primaryColor)"
              : "#d32f2f",
            color: "white",
            fontWeight: 600,
            fontSize: "12px",
            height: "28px",
            minWidth: "60px",
            cursor: isButtonDisabled ? "not-allowed" : "pointer",
            "&:hover": {
              backgroundColor: !isButtonDisabled
                ? color === "primary"
                  ? "var(--primaryColorDark)"
                  : "#b71c1c"
                : "rgba(0, 0, 0, 0.12)",
            },
            "&:disabled": {
              opacity: 0.6,
            },
          }}
        />
      </Tooltip>
    );
  }

  // Compact variant - smaller button
  if (variant === "compact") {
    return (
      <Button
        onClick={handleExport}
        disabled={isButtonDisabled}
        size="small"
        color={color}
        variant="contained"
        startIcon={
          isExporting ? (
            <CircularProgress size={16} color="inherit" />
          ) : (
            <GetAppIcon sx={{ fontSize: "16px" }} />
          )
        }
        sx={{
          minHeight: "32px",
          height: "32px",
          minWidth: "70px",
          fontSize: "11px",
          fontWeight: 600,
          textTransform: "uppercase",
          borderRadius: "4px",
          padding: "4px 8px",
          boxShadow: "none",
          "&:hover": {
            boxShadow: "0 2px 4px rgba(0,0,0,0.2)",
          },
        }}
      >
        PDF
      </Button>
    );
  }

  // Standard button variant
  if (variant === "button") {
    return (
      <Button
        fullWidth
        className="commonShapeBtn"
        onClick={handleExport}
        disabled={isButtonDisabled}
        size={size}
        color={color}
        variant="contained"
        startIcon={
          isExporting ? (
            <CircularProgress
              size={size === "small" ? 16 : size === "large" ? 28 : 20}
              color="inherit"
            />
          ) : (
            <PictureAsPdfIcon />
          )
        }
        style={{ textTransform: "none" }}
      >
        {buttonText}
      </Button>
    );
  }

  return (
    <Tooltip title={isButtonDisabled ? "Export not available" : tooltipText}>
      <Box sx={{ position: "relative", display: "inline-flex" }}>
        <IconButton
          onClick={handleExport}
          disabled={isButtonDisabled}
          size={size}
          color={color}
          sx={{
            textTransform: "none",
            transition: "all 0.2s ease-in-out",
            "&:hover": {
              transform: !isButtonDisabled ? "scale(1.1)" : "none",
              backgroundColor: !isButtonDisabled
                ? "rgba(211, 47, 47, 0.08)"
                : "transparent",
            },
            "&:disabled": {
              opacity: 0.5,
            },
          }}
        >
          {isExporting ? (
            <CircularProgress
              size={size === "small" ? 16 : size === "large" ? 28 : 20}
              color="inherit"
            />
          ) : (
            <PictureAsPdfIcon
              fontSize={
                size === "small"
                  ? "small"
                  : size === "large"
                  ? "large"
                  : "medium"
              }
              sx={{ color: "#d32f2f" }}
            />
          )}
        </IconButton>
      </Box>
    </Tooltip>
  );
};

export default PDFExportButton;
