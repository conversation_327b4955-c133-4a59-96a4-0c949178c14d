import React, { useState } from "react";
import {
  Icon<PERSON>utton,
  Tooltip,
  CircularProgress,
  Box,
  Button,
} from "@mui/material";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";
import {
  PDFExportService,
  FilterCriteria,
  ChartElement,
  SummaryData,
} from "../../services/pdfExport.service";

interface PDFExportButtonProps {
  reportTitle: string;
  filterCriteria: FilterCriteria;
  summaryData?: SummaryData;
  containerSelector?: string;
  chartElements?: ChartElement[];
  filename?: string;
  orientation?: "portrait" | "landscape";
  disabled?: boolean;
  size?: "small" | "medium" | "large";
  variant?: "icon" | "button";
  color?:
    | "primary"
    | "secondary"
    | "inherit"
    | "success"
    | "error"
    | "info"
    | "warning";
  tooltipText?: string;
  buttonText?: string;
  onExportStart?: () => void;
  onExportComplete?: () => void;
  onExportError?: (error: Error) => void;
}

const PDFExportButton: React.FC<PDFExportButtonProps> = ({
  reportTitle,
  filterCriteria,
  summaryData,
  containerSelector = ".analytics-container",
  chartElements,
  filename,
  orientation = "portrait",
  disabled = false,
  size = "medium",
  variant = "icon",
  color = "primary",
  tooltipText = "Export report as PDF",
  buttonText = "Export PDF",
  onExportStart,
  onExportComplete,
  onExportError,
}) => {
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    if (isExporting || disabled) return;

    try {
      setIsExporting(true);
      onExportStart?.();

      let chartsToExport: ChartElement[] = [];

      if (chartElements) {
        // Use provided chart elements
        chartsToExport = chartElements;
      } else {
        // Auto-capture charts from container
        chartsToExport = await PDFExportService.captureChartsFromContainer(
          containerSelector
        );
      }

      if (chartsToExport.length === 0) {
        throw new Error(
          "No charts found to export. Please ensure charts are loaded and try again."
        );
      }

      // Export to PDF
      await PDFExportService.exportReportToPDF({
        reportTitle,
        filterCriteria,
        summaryData,
        chartElements: chartsToExport,
        filename,
        orientation,
      });

      onExportComplete?.();
    } catch (error) {
      console.error("Error exporting PDF:", error);
      const errorMessage =
        error instanceof Error ? error : new Error("Unknown error occurred");
      onExportError?.(errorMessage);
    } finally {
      setIsExporting(false);
    }
  };

  const isButtonDisabled = disabled || isExporting;

  if (variant === "button") {
    return (
      <Button
        fullWidth
        className="commonShapeBtn"
        onClick={handleExport}
        disabled={isButtonDisabled}
        size={size}
        color={color}
        variant="contained"
        startIcon={
          isExporting ? (
            <CircularProgress
              size={size === "small" ? 16 : size === "large" ? 28 : 20}
              color="inherit"
            />
          ) : (
            <PictureAsPdfIcon />
          )
        }
        style={{ textTransform: "none" }}
      >
        {buttonText}
      </Button>
    );
  }

  return (
    <Tooltip title={isButtonDisabled ? "Export not available" : tooltipText}>
      <Box sx={{ position: "relative", display: "inline-flex" }}>
        <IconButton
          onClick={handleExport}
          disabled={isButtonDisabled}
          size={size}
          color={color}
          sx={{
            textTransform: "none",
            transition: "all 0.2s ease-in-out",
            "&:hover": {
              transform: !isButtonDisabled ? "scale(1.1)" : "none",
              backgroundColor: !isButtonDisabled
                ? "rgba(211, 47, 47, 0.08)"
                : "transparent",
            },
            "&:disabled": {
              opacity: 0.5,
            },
          }}
        >
          {isExporting ? (
            <CircularProgress
              size={size === "small" ? 16 : size === "large" ? 28 : 20}
              color="inherit"
            />
          ) : (
            <PictureAsPdfIcon
              fontSize={
                size === "small"
                  ? "small"
                  : size === "large"
                  ? "large"
                  : "medium"
              }
              sx={{ color: "#d32f2f" }}
            />
          )}
        </IconButton>
      </Box>
    </Tooltip>
  );
};

export default PDFExportButton;
