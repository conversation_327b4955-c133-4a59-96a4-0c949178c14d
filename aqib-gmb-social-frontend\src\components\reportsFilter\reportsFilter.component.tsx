import React, { useEffect, useState } from "react";
import {
  Box,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Typography,
  Button,
  IconButton,
} from "@mui/material";

import DateFilter from "../dateFilter/dateFilter.component";
import BusinessService from "../../services/business/business.service";
import { SELECT_DROPDOWN_STYLES } from "../../constants/styles.constant";

import FileDownloadIcon from "@mui/icons-material/FileDownload";
import { useDispatch, useSelector } from "react-redux";
import PDFExportButton from "../pdfExportButton/pdfExportButton.component";
import { FilterCriteria } from "../../services/pdfExport.service";
import {
  IBusiness,
  IBusinessListResponseModel,
} from "../../interfaces/response/IBusinessListResponseModel";
import {
  IBusinessGroup,
  IBusinessGroupsResponseModel,
} from "../../interfaces/response/IBusinessGroupsResponseModel";
import {
  ILocation,
  ILocationsListResponseModel,
} from "../../interfaces/response/ILocationsListResponseModel";
import FilterListIcon from "@mui/icons-material/FilterList";
import FileDownloadOutlinedIcon from "@mui/icons-material/FileDownloadOutlined";

interface IReportsFilterProps {
  onFilterChange: (filters: IReportsFilterData) => void;
  onExport?: () => void;
  showExport?: boolean;
  filterCriteria?: FilterCriteria;
}

export interface IReportsFilterData {
  businessId: string;
  accountId: string;
  locationId: string;
  startDate: string;
  endDate: string;
}

const ReportsFilter: React.FC<IReportsFilterProps> = ({
  onFilterChange,
  onExport,
  showExport = false,
  filterCriteria = {},
}) => {
  const [businessList, setBusinessList] = useState<IBusiness[]>([]);
  const [accountList, setAccountList] = useState<IBusinessGroup[]>([]);
  const [locationList, setLocationList] = useState<ILocation[]>([]);

  const [selectedBusiness, setSelectedBusiness] = useState<string>("0");
  const [selectedAccount, setSelectedAccount] = useState<string>("0");
  const [selectedLocation, setSelectedLocation] = useState<string>("0");
  const [dateRange, setDateRange] = useState<{
    from: string;
    to: string;
    isSameMonthYear: boolean;
  } | null>(null);

  const dispatch = useDispatch();
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const _businessService = new BusinessService(dispatch);

  useEffect(() => {
    fetchBusinessList();
  }, []);

  useEffect(() => {
    if (selectedBusiness !== "0") {
      fetchAccountList();
    } else {
      setAccountList([]);
      setSelectedAccount("0");
      setLocationList([]);
      setSelectedLocation("0");
    }
  }, [selectedBusiness]);

  useEffect(() => {
    if (selectedAccount !== "0") {
      fetchLocationList();
    } else {
      setLocationList([]);
      setSelectedLocation("0");
    }
  }, [selectedAccount]);

  // Validation function
  const isFormValid = () => {
    return (
      selectedBusiness !== "0" &&
      selectedAccount !== "0" &&
      selectedLocation !== "0" &&
      dateRange?.from &&
      dateRange?.to
    );
  };

  // Handle Apply button click
  const handleApplyFilters = () => {
    if (!isFormValid()) {
      return;
    }

    const filters: IReportsFilterData = {
      businessId: selectedBusiness,
      accountId: selectedAccount,
      locationId: selectedLocation,
      startDate: dateRange?.from || "",
      endDate: dateRange?.to || "",
    };
    onFilterChange(filters);
  };

  const fetchBusinessList = async () => {
    try {
      const response: IBusinessListResponseModel =
        await _businessService.getBusiness(userInfo.id);
      if (response.list.length > 0) {
        setBusinessList(response.list);
      }
    } catch (error) {
      console.error("Error fetching business list:", error);
    }
  };

  const fetchAccountList = async () => {
    try {
      const response: IBusinessGroupsResponseModel =
        await _businessService.getBusinessGroups(userInfo.id);
      if (response.data.length > 0) {
        // Filter accounts by selected business
        const filteredAccounts = response.data.filter(
          (account) => account.businessId?.toString() === selectedBusiness
        );
        setAccountList(filteredAccounts);
      }
    } catch (error) {
      console.error("Error fetching account list:", error);
    }
  };

  const fetchLocationList = async () => {
    try {
      const response: ILocationsListResponseModel =
        await _businessService.getLocations(userInfo.id);
      if (response.list.length > 0) {
        // Filter locations by selected account
        const filteredLocations = response.list.filter(
          (location) => location.gmbAccountId === selectedAccount
        );
        setLocationList(filteredLocations);
      }
    } catch (error) {
      console.error("Error fetching location list:", error);
    }
  };

  const handleBusinessChange = (event: SelectChangeEvent) => {
    setSelectedBusiness(event.target.value);
    setSelectedAccount("0");
    setSelectedLocation("0");
  };

  const handleAccountChange = (event: SelectChangeEvent) => {
    setSelectedAccount(event.target.value);
    setSelectedLocation("0");
  };

  const handleLocationChange = (event: SelectChangeEvent) => {
    setSelectedLocation(event.target.value);
  };

  const handleDateChange = (range: {
    from: string;
    to: string;
    isSameMonthYear: boolean;
  }) => {
    setDateRange(range);
  };

  return (
    <Box sx={{ mb: 3 }}>
      {/* <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Typography variant="h6" fontWeight="bold"></Typography>
        {showExport && onExport && (
          <Button
            variant="contained"
            startIcon={<FileDownloadIcon />}
            onClick={onExport}
            sx={{
              minHeight: "40px", // Set the desired height
              capitallize: "none",
              textTransform: "none",
              borderRadius: "5px !important",
            }}
          >
            Export Report
          </Button>
        )}
      </Box> */}

      <Grid container spacing={2} alignItems="flex-end">
        <Grid item xs={12} md={6} lg={4}>
          <FormControl fullWidth variant="outlined" required>
            <InputLabel>Business</InputLabel>
            <Select
              value={selectedBusiness}
              onChange={handleBusinessChange}
              label="Business"
              error={selectedBusiness === "0"}
              sx={SELECT_DROPDOWN_STYLES}
            >
              <MenuItem value="0" disabled>
                Select Business
              </MenuItem>
              {businessList.map((business) => (
                <MenuItem key={business.id} value={business.id.toString()}>
                  {business.businessName}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={6} lg={4}>
          <FormControl fullWidth variant="outlined" required>
            <InputLabel>Account</InputLabel>
            <Select
              value={selectedAccount}
              onChange={handleAccountChange}
              label="Account"
              disabled={selectedBusiness === "0"}
              error={selectedAccount === "0"}
              sx={SELECT_DROPDOWN_STYLES}
            >
              <MenuItem value="0" disabled>
                Select Account
              </MenuItem>
              {accountList.map((account) => (
                <MenuItem key={account.accountId} value={account.accountId}>
                  {account.accountName}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={6} lg={4}>
          <FormControl fullWidth variant="outlined" required>
            <InputLabel>Location</InputLabel>
            <Select
              value={selectedLocation}
              onChange={handleLocationChange}
              label="Location"
              disabled={selectedAccount === "0"}
              error={selectedLocation === "0"}
              sx={SELECT_DROPDOWN_STYLES}
            >
              <MenuItem value="0" disabled>
                Select Location
              </MenuItem>
              {locationList.map((location) => (
                <MenuItem
                  key={location.gmbLocationId}
                  value={location.gmbLocationId}
                >
                  {location.gmbLocationName}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={6} lg={4}>
          <DateFilter onDateChange={handleDateChange} />
        </Grid>

        <Grid item xs={12} md={6} lg={4}>
          <Button
            className="commonShapeBtn"
            variant="contained"
            onClick={handleApplyFilters}
            disabled={!isFormValid()}
            startIcon={<FilterListIcon />}
            fullWidth
          >
            Apply
          </Button>
        </Grid>
        <Grid item xs={12} md={6} lg={2}>
          <Button
            className="commonShapeBtn"
            variant="contained"
            onClick={onExport}
            disabled={!showExport}
            startIcon={<FileDownloadOutlinedIcon />}
            fullWidth
          >
            Excel
          </Button>
        </Grid>

        <Grid item xs={12} md={6} lg={2}>
          <PDFExportButton
            reportTitle="Review Reports"
            filterCriteria={filterCriteria}
            containerSelector=".review-reports-container"
            variant="button"
            buttonText="Pdf"
            disabled={!showExport}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default ReportsFilter;
