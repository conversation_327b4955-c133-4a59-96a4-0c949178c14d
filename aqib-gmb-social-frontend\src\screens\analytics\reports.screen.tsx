import React, { useState, useEffect, useContext } from "react";
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
} from "@mui/material";
import AssessmentIcon from "@mui/icons-material/Assessment";
import StarIcon from "@mui/icons-material/Star";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import PieChartIcon from "@mui/icons-material/PieChart";
import TimelineIcon from "@mui/icons-material/Timeline";

import ReportsFilter, {
  IReportsFilterData,
} from "../../components/reportsFilter/reportsFilter.component";
import RatingsVsMonthChart from "../../components/charts/ratingsVsMonthChart.component";
import ReviewsVsRepliesChart from "../../components/charts/reviewsVsRepliesChart.component";
import RatingDistributionChart from "../../components/charts/ratingDistributionChart.component";
import ReviewVolumeChart from "../../components/charts/reviewVolumeChart.component";
import PerformanceSummaryCard from "../../components/charts/performanceSummaryCard.component";
import DailyVolumeTrendChart from "../../components/charts/dailyVolumeTrendChart.component";
import ResponseRateTrendChart from "../../components/charts/responseRateTrendChart.component";
import ResponseTimeAnalysisChart from "../../components/charts/responseTimeAnalysisChart.component";
import RatingTrendsChart from "../../components/charts/ratingTrendsChart.component";
import ReviewsPerformanceFilter, {
  IReviewsPerformanceFilterData,
} from "../../components/performanceFilter/performanceFilter.component";

import ReportsService, {
  IReviewsReportResponse,
  IPerformanceReportResponse,
} from "../../services/reports/reports.service";
import { LoadingContext } from "../../context/loading.context";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import { useDispatch } from "react-redux";
import LeftMenuComponent from "../../components/leftMenu/leftMenu.component";
import PDFExportButton from "../../components/pdfExportButton/pdfExportButton.component";
import { FilterCriteria } from "../../services/pdfExport.service";

interface IExtendedPageProps {
  title: string;
}

const Reports: React.FC<IExtendedPageProps> = ({ title }) => {
  const [reportData, setReportData] = useState<
    IReviewsReportResponse["data"] | null
  >(null);
  const [performanceData, setPerformanceData] = useState<
    IPerformanceReportResponse["data"] | null
  >(null);
  const [currentFilters, setCurrentFilters] =
    useState<IReportsFilterData | null>(null);
  const [currentPerformanceFilters, setCurrentPerformanceFilters] =
    useState<IReviewsPerformanceFilterData | null>(null);

  const { setLoading } = useContext(LoadingContext);
  const { setToastConfig } = useContext(ToastContext);
  const dispatch = useDispatch();
  const _reportsService = new ReportsService(dispatch);

  useEffect(() => {
    document.title = title;
  }, [title]);

  // No automatic data loading - wait for user to apply filters

  const handleFilterChange = async (filters: IReportsFilterData) => {
    console.log("Filter change triggered:", filters);
    setCurrentFilters(filters);
    await fetchReviewsReport(filters);
  };

  const fetchReviewsReport = async (filters: IReportsFilterData) => {
    try {
      console.log("Fetching reviews report with filters:", filters);
      setLoading(true);
      const response = await _reportsService.getReviewsReport(filters);
      console.log("Reports API response:", response);

      if (response.success) {
        setReportData(response.data);
        console.log("Report data set:", response.data);
      } else {
        console.error("API returned error:", response);
        setToastConfig(
          ToastSeverity.Error,
          response.message || "Failed to fetch report data",
          true
        );
      }
    } catch (error) {
      console.error("Error fetching reviews report:", error);
      setToastConfig(ToastSeverity.Error, "Failed to fetch report data", true);
    } finally {
      setLoading(false);
    }
  };

  // Prepare filter criteria for PDF export
  const getFilterCriteria = (): FilterCriteria => {
    if (!currentFilters) return {};

    return {
      businessName:
        currentFilters.businessId !== "0"
          ? "Selected Business"
          : "All Businesses",
      accountName:
        currentFilters.accountId !== "0" ? "Selected Account" : "All Accounts",
      locationName:
        currentFilters.locationId !== "0"
          ? "Selected Location"
          : "All Locations",
      fromDate: currentFilters.startDate || "",
      toDate: currentFilters.endDate || "",
    };
  };

  const handleExport = async () => {
    if (!currentFilters || !reportData) {
      setToastConfig(
        ToastSeverity.Warning,
        "No data available to export. Please apply filters first.",
        true
      );
      return;
    }

    try {
      setLoading(true);

      // Use frontend Excel export functionality instead of backend API
      await exportReportDataToExcel();

      setToastConfig(
        ToastSeverity.Success,
        "Report exported successfully",
        true
      );
    } catch (error) {
      console.error("Error exporting report:", error);
      setToastConfig(ToastSeverity.Error, "Failed to export report", true);
    } finally {
      setLoading(false);
    }
  };

  const exportReportDataToExcel = async () => {
    if (!reportData) {
      throw new Error("No report data available");
    }

    try {
      // Create workbook
      const XLSX = await import("xlsx");
      const { saveAs } = await import("file-saver");

      const workbook = XLSX.utils.book_new();

      // Prepare summary data
      const summaryData = [
        ["Reviews Report Summary"],
        [],
        ["Export Date", new Date().toLocaleString()],
        ["Business", currentFilters?.businessId || "All"],
        ["Account", currentFilters?.accountId || "All"],
        ["Location", currentFilters?.locationId || "All"],
        [
          "Date Range",
          `${currentFilters?.startDate} to ${currentFilters?.endDate}`,
        ],
        [],
        ["Report Statistics"],
        ["Total Reviews", reportData.reviews?.length || 0],
        [
          "Response Rate",
          `${Math.round(
            (reportData.aggregated.responseRate.replied /
              reportData.aggregated.responseRate.total) *
              100
          )}%`,
        ],
      ];

      // Create summary sheet
      const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, summarySheet, "Summary");

      // Prepare ratings vs month data
      const ratingsData: any[][] = [
        ["Month", "1 Star", "2 Stars", "3 Stars", "4 Stars", "5 Stars"],
      ];
      Object.entries(reportData.aggregated.ratingsVsMonth).forEach(
        ([month, ratings]: [string, any]) => {
          ratingsData.push([
            month,
            ratings[1] || 0,
            ratings[2] || 0,
            ratings[3] || 0,
            ratings[4] || 0,
            ratings[5] || 0,
          ]);
        }
      );
      const ratingsSheet = XLSX.utils.aoa_to_sheet(ratingsData);
      XLSX.utils.book_append_sheet(workbook, ratingsSheet, "Ratings by Month");

      // Prepare reviews vs replies data
      const reviewsRepliesData: any[][] = [["Month", "Reviews", "Replies"]];
      Object.entries(reportData.aggregated.reviewsVsReplies).forEach(
        ([month, data]: [string, any]) => {
          reviewsRepliesData.push([month, data.reviews, data.replies]);
        }
      );
      const reviewsRepliesSheet = XLSX.utils.aoa_to_sheet(reviewsRepliesData);
      XLSX.utils.book_append_sheet(
        workbook,
        reviewsRepliesSheet,
        "Reviews vs Replies"
      );

      // Prepare rating distribution data
      const distributionData: any[][] = [["Rating", "Count"]];
      Object.entries(reportData.aggregated.ratingDistribution).forEach(
        ([rating, count]: [string, any]) => {
          distributionData.push([
            `${rating} Star${rating !== "1" ? "s" : ""}`,
            count,
          ]);
        }
      );
      const distributionSheet = XLSX.utils.aoa_to_sheet(distributionData);
      XLSX.utils.book_append_sheet(
        workbook,
        distributionSheet,
        "Rating Distribution"
      );

      // Prepare review volume data
      const volumeData: any[][] = [["Date", "Review Count"]];
      Object.entries(reportData.aggregated.reviewVolume).forEach(
        ([date, count]: [string, any]) => {
          volumeData.push([date, count]);
        }
      );
      const volumeSheet = XLSX.utils.aoa_to_sheet(volumeData);
      XLSX.utils.book_append_sheet(workbook, volumeSheet, "Review Volume");

      // Prepare detailed reviews data if available
      if (reportData.reviews && reportData.reviews.length > 0) {
        const reviewsDetailData: any[][] = [
          [
            "Date",
            "Rating",
            "Review Text",
            "Reviewer Name",
            "Reply Text",
            "Reply Date",
          ],
        ];

        reportData.reviews.forEach((review: any) => {
          reviewsDetailData.push([
            review.created_at
              ? new Date(review.created_at).toLocaleDateString()
              : "",
            review.rating || "",
            review.review_text || "",
            review.reviewer_name || "",
            review.reply_text || "",
            review.reply_date
              ? new Date(review.reply_date).toLocaleDateString()
              : "",
          ]);
        });

        const reviewsDetailSheet = XLSX.utils.aoa_to_sheet(reviewsDetailData);
        XLSX.utils.book_append_sheet(
          workbook,
          reviewsDetailSheet,
          "Detailed Reviews"
        );
      }

      // Generate filename with timestamp
      const timestamp = new Date()
        .toISOString()
        .slice(0, 19)
        .replace(/:/g, "-");
      const filename = `Reviews_Report_${timestamp}.xlsx`;

      // Export the file
      const excelBuffer = XLSX.write(workbook, {
        bookType: "xlsx",
        type: "array",
      });

      const blob = new Blob([excelBuffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      // Use fallback download method
      try {
        saveAs(blob, filename);
      } catch (saveAsError) {
        console.error("saveAs failed, trying alternative method:", saveAsError);

        // Fallback download method
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = filename;
        link.style.display = "none";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error("Error in exportReportDataToExcel:", error);
      throw error;
    }
  };

  return (
    <div>
      <LeftMenuComponent>
        <Box
          sx={{
            pr: 1,
          }}
        >
          <Box className="page">
            <Box className="pageContainer">
              <Box className="rightSideContainer">
                <Box className="pageContent">
                  <Box>
                    <h3 className="pageTitle">Google Review Reports</h3>
                    <Typography variant="subtitle2" className="subtitle2">
                      Comprehensive reporting dashboard for business insights
                    </Typography>
                    {/* <Typography variant="h4" className="pageTitle">
                      Review Reports
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      Comprehensive reporting dashboard for business insights
                    </Typography> */}
                  </Box>
                  <Divider></Divider>
                  <Grid container spacing={2} marginTop={1}>
                    {/* Right Content - Reports */}
                    <Grid item xs={12} md={12}>
                      <Box>
                        {/* Filters */}
                        <ReportsFilter
                          onFilterChange={handleFilterChange}
                          onExport={handleExport}
                          showExport={reportData ? true : false}
                          filterCriteria={getFilterCriteria()}
                        />

                        {/* Charts */}
                        {reportData && (
                          <Grid
                            container
                            spacing={3}
                            className="review-reports-container"
                          >
                            <Grid item xs={12} lg={6}>
                              <RatingsVsMonthChart
                                data={reportData.aggregated.ratingsVsMonth}
                              />
                            </Grid>

                            <Grid item xs={12} lg={6}>
                              <ReviewsVsRepliesChart
                                data={reportData.aggregated.reviewsVsReplies}
                              />
                            </Grid>

                            <Grid item xs={12} lg={6}>
                              <RatingDistributionChart
                                data={reportData.aggregated.ratingDistribution}
                              />
                            </Grid>

                            <Grid item xs={12} lg={6}>
                              <ReviewVolumeChart
                                data={reportData.aggregated.reviewVolume}
                              />
                            </Grid>
                          </Grid>
                        )}

                        {!reportData && (
                          <Box
                            sx={{
                              display: "flex",
                              flexDirection: "column",
                              justifyContent: "center",
                              alignItems: "center",
                              height: 400,
                              borderRadius: 2,
                              textAlign: "center",
                              p: 3,
                            }}
                          >
                            <Typography
                              variant="h6"
                              color="text.secondary"
                              gutterBottom
                            >
                              Select Required Filters to Generate Reports
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Please select Business, Account, Locations, and
                              Duration, then click "Apply Filters" to view your
                              reports.
                            </Typography>
                          </Box>
                        )}
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      </LeftMenuComponent>
    </div>
  );
};

export default Reports;
