import React from "react";
import { Box, Typography, useTheme } from "@mui/material";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar } from "react-chartjs-2";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface IReviewsVsRepliesChartProps {
  data: Record<string, { reviews: number; replies: number }>;
  title?: string;
}

const ReviewsVsRepliesChart: React.FC<IReviewsVsRepliesChartProps> = ({
  data,
  title = "Reviews vs Replies by Month",
}) => {
  const theme = useTheme();

  // Prepare chart data
  const months = Object.keys(data).sort();

  const chartData = {
    labels: months,
    datasets: [
      {
        label: "Total Reviews",
        data: months.map((month) => data[month]?.reviews || 0),
        backgroundColor: theme.palette.primary.main,
        borderColor: theme.palette.primary.main,
        borderWidth: 1,
      },
      {
        label: "Replied Reviews",
        data: months.map((month) => data[month]?.replies || 0),
        backgroundColor: theme.palette.secondary.main,
        borderColor: theme.palette.secondary.main,
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: "top" as const,
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  // Calculate totals and response rate
  const totalReviews = months.reduce(
    (total, month) => total + (data[month]?.reviews || 0),
    0
  );
  const totalReplies = months.reduce(
    (total, month) => total + (data[month]?.replies || 0),
    0
  );
  const responseRate =
    totalReviews > 0 ? ((totalReplies / totalReviews) * 100).toFixed(1) : "0.0";

  return (
    <Box
      sx={{ p: 2, backgroundColor: "#fff", borderRadius: 2 }}
      data-chart-type="reviewsvsreplies"
      data-chart-title={title}
    >
      <Box sx={{ mb: 2 }}>
        <Typography
          variant="h6"
          fontWeight="bold"
          gutterBottom
          className="chart-title"
        >
          {title}
        </Typography>
        <Box sx={{ display: "flex", gap: 3, mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Total Reviews: <strong>{totalReviews}</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Total Replies: <strong>{totalReplies}</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Response Rate: <strong>{responseRate}%</strong>
          </Typography>
        </Box>
      </Box>

      {totalReviews > 0 ? (
        <Bar data={chartData} options={options} />
      ) : (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: 300,
            color: "text.secondary",
          }}
        >
          <Typography variant="body1">
            No data available for the selected filters
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default ReviewsVsRepliesChart;
