import React from "react";
import { Box, Typography, useTheme } from "@mui/material";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Line } from "react-chartjs-2";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface IReviewVolumeChartProps {
  data: Record<string, number>;
  title?: string;
}

const ReviewVolumeChart: React.FC<IReviewVolumeChartProps> = ({
  data,
  title = "Review Volume Trends",
}) => {
  const theme = useTheme();

  // Prepare chart data
  const months = Object.keys(data).sort();
  const values = months.map((month) => data[month] || 0);

  // Convert hex to rgba for background
  const hexToRgba = (hex: string, alpha: number) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  };

  const primaryColor = theme.palette.primary.main;
  const primaryAlpha =
    theme.palette.primaryAlpha?.main || hexToRgba(primaryColor, 0.1);

  const chartData = {
    labels: months,
    datasets: [
      {
        label: "Reviews",
        data: values,
        borderColor: primaryColor,
        backgroundColor: primaryAlpha,
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: primaryColor,
        pointBorderColor: "#fff",
        pointBorderWidth: 2,
        pointRadius: 6,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: "rgba(0, 0, 0, 0.1)",
        },
      },
      x: {
        grid: {
          color: "rgba(0, 0, 0, 0.1)",
        },
      },
    },
  };

  // Calculate statistics
  const totalReviews = values.reduce((sum, val) => sum + val, 0);
  const averagePerMonth =
    months.length > 0 ? (totalReviews / months.length).toFixed(1) : "0.0";
  const maxMonth =
    months.length > 0 ? months[values.indexOf(Math.max(...values))] : "N/A";
  const maxValue = Math.max(...values);

  return (
    <Box
      sx={{ p: 2, backgroundColor: "#fff", borderRadius: 2 }}
      data-chart-type="reviewvolume"
      data-chart-title={title}
    >
      <Box sx={{ mb: 2 }}>
        <Typography
          variant="h6"
          fontWeight="bold"
          gutterBottom
          className="chart-title"
        >
          {title}
        </Typography>
        <Box sx={{ display: "flex", gap: 3, mb: 2, flexWrap: "wrap" }}>
          <Typography variant="body2" color="text.secondary">
            Total Reviews: <strong>{totalReviews}</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Avg/Month: <strong>{averagePerMonth}</strong>
          </Typography>
          {maxValue > 0 && (
            <Typography variant="body2" color="text.secondary">
              Peak Month:{" "}
              <strong>
                {maxMonth} ({maxValue} reviews)
              </strong>
            </Typography>
          )}
        </Box>
      </Box>

      {totalReviews > 0 ? (
        <Line data={chartData} options={options} />
      ) : (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: 300,
            color: "text.secondary",
          }}
        >
          <Typography variant="body1">
            No data available for the selected filters
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default ReviewVolumeChart;
