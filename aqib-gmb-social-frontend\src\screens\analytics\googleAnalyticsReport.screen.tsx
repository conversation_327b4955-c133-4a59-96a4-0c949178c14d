import React, { useContext, useState, useEffect } from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Divider,
} from "@mui/material";
import LeftMenuComponent from "../../components/leftMenu/leftMenu.component";
import GoogleAnalyticsFilter, {
  IGoogleAnalyticsFilterData,
} from "../../components/googleAnalyticsFilter/googleAnalyticsFilter.component";
import GoogleAnalyticsCharts from "../../components/charts/googleAnalyticsCharts.component";
import { LoadingContext } from "../../context/loading.context";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import { useDispatch, useSelector } from "react-redux";
import ReportsService from "../../services/reports/reports.service";
import BusinessService from "../../services/business/business.service";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import TrendingDownIcon from "@mui/icons-material/TrendingDown";
import TrendingFlatIcon from "@mui/icons-material/TrendingFlat";
import {
  ILocation,
  ILocationsListResponseModel,
} from "../../interfaces/response/ILocationsListResponseModel";
import PDFExportButton from "../../components/pdfExportButton/pdfExportButton.component";
import { FilterCriteria } from "../../services/pdfExport.service";

interface IExtendedPageProps {
  title: string;
}

interface IGoogleAnalyticsData {
  summary: {
    totalImpressions: number;
    totalClicks: number;
    totalCalls: number;
    totalDirections: number;
    totalWebsiteClicks: number;
    totalMessaging: number;
    totalBookings: number;
    clickThroughRate: number;
    trends: {
      impressions: {
        value: number;
        direction: "up" | "down" | "flat";
      };
      clicks: {
        value: number;
        direction: "up" | "down" | "flat";
      };
      calls: {
        value: number;
        direction: "up" | "down" | "flat";
      };
      directions: {
        value: number;
        direction: "up" | "down" | "flat";
      };
      websiteClicks: {
        value: number;
        direction: "up" | "down" | "flat";
      };
      messaging: {
        value: number;
        direction: "up" | "down" | "flat";
      };
      bookings: {
        value: number;
        direction: "up" | "down" | "flat";
      };
    };
  };
  charts: {
    impressionsOverTime: { data: number[]; labels: string[] };
    clicksOverTime: { data: number[]; labels: string[] };
    callsOverTime: { data: number[]; labels: string[] };
    directionsOverTime: { data: number[]; labels: string[] };
    websiteClicksOverTime: { data: number[]; labels: string[] };
    messagingOverTime: { data: number[]; labels: string[] };
    bookingsOverTime: { data: number[]; labels: string[] };
    platformBreakdown: { label: string; value: number; percentage: number }[];
    searchQueries: { query: string; impressions: number; clicks: number }[];
  };
  dateRange: { fromDate: string; toDate: string };
}

const GoogleAnalyticsReport: React.FC<IExtendedPageProps> = ({ title }) => {
  const [analyticsData, setAnalyticsData] =
    useState<IGoogleAnalyticsData | null>(null);
  const [currentFilters, setCurrentFilters] =
    useState<IGoogleAnalyticsFilterData | null>(null);
  const [locationList, setLocationList] = useState<ILocation[]>([]);

  const { setLoading } = useContext(LoadingContext);
  const { setToastConfig } = useContext(ToastContext);
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state: any) => state.authReducer);

  const _reportsService = new ReportsService(dispatch);
  const _businessService = new BusinessService(dispatch);

  useEffect(() => {
    document.title = title;
    if (userInfo?.id) {
      fetchLocationList();
    }
  }, [title, userInfo]);

  const fetchLocationList = async () => {
    if (!userInfo?.id) {
      console.warn("User info not available, skipping location fetch");
      return;
    }

    try {
      const response: ILocationsListResponseModel =
        await _businessService.getLocations(userInfo.id);
      if (response.list.length > 0) {
        setLocationList(response.list);
      }
    } catch (error) {
      console.error("Error fetching location list:", error);
    }
  };

  const handleFilterChange = async (filters: IGoogleAnalyticsFilterData) => {
    setCurrentFilters(filters);
    await fetchGoogleAnalyticsReport(filters);
  };

  const fetchGoogleAnalyticsReport = async (
    filters: IGoogleAnalyticsFilterData
  ) => {
    try {
      setLoading(true);

      // Call the new Google Analytics chart API endpoint
      const chartsResponse = await _reportsService.getGoogleAnalyticsCharts(
        filters
      );

      if (chartsResponse.success) {
        // Transform chart data to match the expected format
        const chartData = chartsResponse.chartData;

        // Create analytics data structure using the new backend response
        const analyticsData: IGoogleAnalyticsData = {
          summary: chartData.summary || {
            totalImpressions: chartData.impressionsOverTime.data.reduce(
              (a: number, b: number) => a + b,
              0
            ),
            totalClicks: chartData.clicksOverTime.data.reduce(
              (a: number, b: number) => a + b,
              0
            ),
            totalCalls: chartData.callsOverTime.data.reduce(
              (a: number, b: number) => a + b,
              0
            ),
            totalDirections: chartData.directionsOverTime.data.reduce(
              (a: number, b: number) => a + b,
              0
            ),
            totalWebsiteClicks: chartData.websiteClicksOverTime.data.reduce(
              (a: number, b: number) => a + b,
              0
            ),
            totalMessaging: chartData.messagingOverTime.data.reduce(
              (a: number, b: number) => a + b,
              0
            ),
            totalBookings: chartData.bookingsOverTime.data.reduce(
              (a: number, b: number) => a + b,
              0
            ),
            clickThroughRate: 0,
            trends: {
              impressions: { value: 0, direction: "flat" },
              clicks: { value: 0, direction: "flat" },
            },
          },
          charts: {
            impressionsOverTime: chartData.impressionsOverTime,
            clicksOverTime: chartData.clicksOverTime,
            callsOverTime: chartData.callsOverTime,
            directionsOverTime: chartData.directionsOverTime,
            websiteClicksOverTime: chartData.websiteClicksOverTime,
            messagingOverTime: chartData.messagingOverTime,
            bookingsOverTime: chartData.bookingsOverTime,
            platformBreakdown: chartData.platformBreakdown,
            searchQueries: chartData.searchQueries,
          },
          dateRange: {
            fromDate: filters.fromDate,
            toDate: filters.toDate,
          },
        };

        setAnalyticsData(analyticsData);
      } else {
        console.error("API returned error:", chartsResponse);
        setToastConfig(
          ToastSeverity.Error,
          chartsResponse.message || "Failed to fetch Google Analytics data",
          true
        );
      }
    } catch (error) {
      console.error("Error fetching Google Analytics report:", error);
      setToastConfig(
        ToastSeverity.Error,
        "Failed to fetch Google Analytics data",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  // Prepare filter criteria for PDF export
  const getFilterCriteria = (): FilterCriteria => {
    if (!currentFilters) return {};

    const selectedLocation = locationList.find(
      (loc) => loc.gmbLocationId === currentFilters.locationId
    );

    return {
      businessName:
        currentFilters.businessId !== "0"
          ? "Selected Business"
          : "All Businesses",
      accountName:
        currentFilters.accountId !== "0" ? "Selected Account" : "All Accounts",
      locationName: selectedLocation?.gmbLocationName || "All Locations",
      fromDate: currentFilters.fromDate || "",
      toDate: currentFilters.toDate || "",
    };
  };

  const handleExport = async () => {
    if (!currentFilters || !analyticsData) {
      setToastConfig(
        ToastSeverity.Warning,
        "No analytics data available to export. Please analyze data first.",
        true
      );
      return;
    }

    try {
      setLoading(true);

      // Call export API
      await _reportsService.exportGoogleAnalyticsReport(currentFilters);

      setToastConfig(
        ToastSeverity.Success,
        "Google Analytics report exported successfully",
        true
      );
    } catch (error) {
      setToastConfig(
        ToastSeverity.Error,
        "Failed to export Google Analytics report",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatPercentage = (num: number | string) => {
    const numValue = typeof num === "string" ? parseFloat(num) : num;
    if (isNaN(numValue)) return "0%";
    return `${numValue >= 0 ? "+" : ""}${numValue.toFixed(1)}%`;
  };

  const getTrendIcon = (direction: string | number) => {
    if (typeof direction === "string") {
      if (direction === "up") return <TrendingUpIcon color="success" />;
      if (direction === "down") return <TrendingDownIcon color="error" />;
      return <TrendingFlatIcon color="disabled" />;
    }
    // Legacy support for numeric values
    if (direction > 0) return <TrendingUpIcon color="success" />;
    if (direction < 0) return <TrendingDownIcon color="error" />;
    return <TrendingFlatIcon color="disabled" />;
  };

  const getTrendColor = (direction: string | number) => {
    if (typeof direction === "string") {
      if (direction === "up") return "success.main";
      if (direction === "down") return "error.main";
      return "text.secondary";
    }
    // Legacy support for numeric values
    if (direction > 0) return "success.main";
    if (direction < 0) return "error.main";
    return "text.secondary";
  };

  return (
    <div>
      <LeftMenuComponent>
        <Box sx={{ pr: 1 }}>
          <Box sx={{ marginBottom: "20px" }}>
            <h3 className="pageTitle">Google Analytics Report</h3>
            <Typography variant="subtitle2" className="subtitle2">
              Comprehensive analytics for your Google Business Profile
              performance
            </Typography>
          </Box>
          <Divider></Divider>
          {/* Filter Component */}
          <GoogleAnalyticsFilter
            onFilterChange={handleFilterChange}
            onExport={handleExport}
            showExport={!!analyticsData}
            filterCriteria={getFilterCriteria()}
          />

          {!analyticsData && (
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
                height: 400,
                borderRadius: 2,
                textAlign: "center",
                p: 3,
              }}
            >
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Select Required Filters to Generate Google Analytics Report
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Please select Business, Account, Location, and Date Range, then
                click "Analyze Google Analytics" to view your analytics data.
              </Typography>
            </Box>
          )}

          {analyticsData && (
            <Box className="google-analytics-container">
              {/* Summary Cards */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <Card>
                    <CardContent>
                      <Typography color="text.secondary" gutterBottom>
                        Total Impressions
                      </Typography>
                      <Typography variant="h4" component="div">
                        {formatNumber(analyticsData.summary.totalImpressions)}
                      </Typography>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mt: 1 }}
                      >
                        {getTrendIcon(
                          analyticsData.summary?.trends?.impressions
                            ?.direction || "flat"
                        )}
                        <Typography
                          variant="body2"
                          sx={{
                            ml: 1,
                            color: getTrendColor(
                              analyticsData.summary?.trends?.impressions
                                ?.direction || "flat"
                            ),
                          }}
                        >
                          {formatPercentage(
                            analyticsData.summary?.trends?.impressions?.value ||
                              0
                          )}
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Card>
                    <CardContent>
                      <Typography color="text.secondary" gutterBottom>
                        Total Clicks
                      </Typography>
                      <Typography variant="h4" component="div">
                        {formatNumber(analyticsData.summary.totalClicks)}
                      </Typography>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mt: 1 }}
                      >
                        {getTrendIcon(
                          analyticsData.summary.trends.clicks.direction
                        )}
                        <Typography
                          variant="body2"
                          sx={{
                            ml: 1,
                            color: getTrendColor(
                              analyticsData.summary.trends.clicks.direction
                            ),
                          }}
                        >
                          {formatPercentage(
                            analyticsData.summary?.trends?.clicks?.value || 0
                          )}
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Card>
                    <CardContent>
                      <Typography color="text.secondary" gutterBottom>
                        Phone Calls
                      </Typography>
                      <Typography variant="h4" component="div">
                        {formatNumber(analyticsData.summary.totalCalls)}
                      </Typography>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mt: 1 }}
                      >
                        {getTrendIcon(
                          analyticsData.summary?.trends?.calls?.direction ||
                            "flat"
                        )}
                        <Typography
                          variant="body2"
                          sx={{
                            ml: 1,
                            color: getTrendColor(
                              analyticsData.summary?.trends?.calls?.direction ||
                                "flat"
                            ),
                          }}
                        >
                          {formatPercentage(
                            analyticsData.summary?.trends?.calls?.value || 0
                          )}
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Card>
                    <CardContent>
                      <Typography color="text.secondary" gutterBottom>
                        Click-Through Rate
                      </Typography>
                      <Typography variant="h4" component="div">
                        {analyticsData.summary.clickThroughRate}%
                      </Typography>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mt: 1 }}
                      >
                        {getTrendIcon(
                          analyticsData.summary.trends.clicks.direction
                        )}
                        <Typography
                          variant="body2"
                          sx={{
                            ml: 1,
                            color: getTrendColor(
                              analyticsData.summary.trends.clicks.direction
                            ),
                          }}
                        >
                          {formatPercentage(
                            analyticsData.summary?.trends?.clicks?.value || 0
                          )}
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              <Divider sx={{ my: 3 }} />

              {/* Charts */}
              <GoogleAnalyticsCharts
                impressionsData={analyticsData.charts.impressionsOverTime}
                clicksData={analyticsData.charts.clicksOverTime}
                callsData={analyticsData.charts.callsOverTime}
                directionsData={analyticsData.charts.directionsOverTime}
                websiteClicksData={analyticsData.charts.websiteClicksOverTime}
                messagingData={analyticsData.charts.messagingOverTime}
                bookingsData={analyticsData.charts.bookingsOverTime}
                platformBreakdownData={analyticsData.charts.platformBreakdown}
                searchQueriesData={analyticsData.charts.searchQueries}
                showExport={true}
                selectedLocationIds={
                  currentFilters?.locationId ? [currentFilters.locationId] : []
                }
                availableLocations={locationList}
                locationDataMap={{}}
                dateRange={
                  currentFilters
                    ? {
                        from: currentFilters.fromDate,
                        to: currentFilters.toDate,
                      }
                    : undefined
                }
              />
            </Box>
          )}
        </Box>
      </LeftMenuComponent>
    </div>
  );
};

export default GoogleAnalyticsReport;
