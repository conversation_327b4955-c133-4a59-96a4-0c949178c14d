<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Export Button - Compact Design Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8rem;
            font-weight: 500;
        }
        
        .button-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .button-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            border: 1px solid #e9ecef;
            transition: transform 0.2s ease;
        }
        
        .button-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .button-card h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .button-card p {
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .button-examples {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }
        
        .specs {
            font-size: 0.85rem;
            color: #6c757d;
            background: white;
            padding: 10px;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }
        
        /* Button Styles */
        .btn {
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        /* Original Button */
        .btn-original {
            background: #007bff;
            color: white;
            padding: 16px 24px;
            font-size: 16px;
            min-height: 54px;
            border-radius: 5px;
        }
        
        .btn-original:hover {
            background: #0056b3;
        }
        
        /* Compact Button */
        .btn-compact {
            background: #dc3545;
            color: white;
            padding: 8px 12px;
            font-size: 11px;
            min-height: 32px;
            min-width: 70px;
        }
        
        .btn-compact:hover {
            background: #c82333;
        }
        
        /* Chip Button */
        .btn-chip {
            background: #d32f2f;
            color: white;
            padding: 6px 12px;
            font-size: 12px;
            height: 28px;
            min-width: 60px;
            border-radius: 14px;
        }
        
        .btn-chip:hover {
            background: #b71c1c;
        }
        
        /* Icon Button */
        .btn-icon {
            background: #6c757d;
            color: white;
            padding: 8px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            justify-content: center;
        }
        
        .btn-icon:hover {
            background: #5a6268;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .comparison-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .comparison-item h4 {
            margin-bottom: 15px;
            color: #495057;
        }
        
        .usage-example {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .usage-example h4 {
            margin-bottom: 15px;
            color: #495057;
        }
        
        .filter-bar {
            display: flex;
            gap: 10px;
            align-items: center;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        
        .filter-bar span {
            color: #6c757d;
            font-size: 14px;
        }
        
        .divider {
            width: 1px;
            height: 20px;
            background: #dee2e6;
        }
        
        .icon {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }
        
        .benefits {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            padding: 30px;
            border-radius: 8px;
            margin-top: 30px;
        }
        
        .benefits h3 {
            color: #155724;
            margin-bottom: 20px;
        }
        
        .benefits ul {
            list-style: none;
            padding: 0;
        }
        
        .benefits li {
            color: #155724;
            margin-bottom: 10px;
            padding-left: 25px;
            position: relative;
        }
        
        .benefits li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PDF Export Button</h1>
            <p>Compact Design Variants for Better Space Utilization</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>Design Variants</h2>
                
                <div class="button-showcase">
                    <div class="button-card">
                        <h3>Chip Variant (Most Compact)</h3>
                        <p>Ultra-compact chip design perfect for tight spaces and filter bars.</p>
                        <div class="button-examples">
                            <button class="btn btn-chip">
                                <svg class="icon" viewBox="0 0 24 24">
                                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                                </svg>
                                PDF
                            </button>
                        </div>
                        <div class="specs">
                            <strong>Dimensions:</strong> 28px height × 60px min width<br>
                            <strong>Best for:</strong> Toolbars, compact layouts, filter bars
                        </div>
                    </div>
                    
                    <div class="button-card">
                        <h3>Compact Variant</h3>
                        <p>Small button design offering good balance between size and visibility.</p>
                        <div class="button-examples">
                            <button class="btn btn-compact">
                                <svg class="icon" viewBox="0 0 24 24">
                                    <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>
                                </svg>
                                PDF
                            </button>
                        </div>
                        <div class="specs">
                            <strong>Dimensions:</strong> 32px height × 70px min width<br>
                            <strong>Best for:</strong> Action bars, secondary actions
                        </div>
                    </div>
                    
                    <div class="button-card">
                        <h3>Icon Only</h3>
                        <p>Minimal icon-only design for maximum space efficiency.</p>
                        <div class="button-examples">
                            <button class="btn btn-icon">
                                <svg class="icon" viewBox="0 0 24 24">
                                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                                </svg>
                            </button>
                        </div>
                        <div class="specs">
                            <strong>Dimensions:</strong> 40px × 40px<br>
                            <strong>Best for:</strong> Icon toolbars, minimal interfaces
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>Size Comparison</h2>
                <div class="comparison-grid">
                    <div class="comparison-item">
                        <h4>Original (54px)</h4>
                        <button class="btn btn-original">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                            </svg>
                            PDF
                        </button>
                    </div>
                    <div class="comparison-item">
                        <h4>Compact (32px)</h4>
                        <button class="btn btn-compact">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>
                            </svg>
                            PDF
                        </button>
                    </div>
                    <div class="comparison-item">
                        <h4>Chip (28px)</h4>
                        <button class="btn btn-chip">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>
                            </svg>
                            PDF
                        </button>
                    </div>
                    <div class="comparison-item">
                        <h4>Icon (40px)</h4>
                        <button class="btn btn-icon">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>Usage Examples</h2>
                
                <div class="usage-example">
                    <h4>In Filter Bars (Recommended: Chip)</h4>
                    <div class="filter-bar">
                        <span>Apply</span>
                        <div class="divider"></div>
                        <span>Excel</span>
                        <div class="divider"></div>
                        <button class="btn btn-chip">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>
                            </svg>
                            PDF
                        </button>
                    </div>
                </div>
                
                <div class="usage-example">
                    <h4>In Action Toolbars (Recommended: Compact)</h4>
                    <div class="filter-bar">
                        <span>Actions:</span>
                        <button class="btn btn-compact">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>
                            </svg>
                            PDF
                        </button>
                        <button class="btn btn-compact" style="background: #6f42c1;">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>
                            </svg>
                            EXCEL
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="benefits">
                <h3>Benefits of Compact Design</h3>
                <ul>
                    <li>Saves 40-50% horizontal space compared to original button</li>
                    <li>Better visual hierarchy in filter and action bars</li>
                    <li>Maintains accessibility and usability standards</li>
                    <li>Consistent with modern UI design trends</li>
                    <li>Responsive design friendly</li>
                    <li>Reduces visual clutter in dense interfaces</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
