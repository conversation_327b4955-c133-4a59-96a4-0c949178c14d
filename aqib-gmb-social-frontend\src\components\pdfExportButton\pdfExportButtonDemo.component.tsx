import React from "react";
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Stack,
  Divider,
} from "@mui/material";
import PDFExportButton from "./pdfExportButton.component";
import { FilterCriteria } from "../../services/pdfExport.service";

const PDFExportButtonDemo: React.FC = () => {
  const mockFilterCriteria: FilterCriteria = {
    businessName: "Demo Business",
    accountName: "Demo Account",
    locationNames: ["Location 1", "Location 2"],
    dateRange: {
      from: "2024-01-01",
      to: "2024-12-31",
    },
  };

  const mockSummaryData = {
    title: "Demo Summary",
    statistics: [
      { label: "Total Impressions", value: 1250 },
      { label: "Total Clicks", value: 89 },
      { label: "Phone Calls", value: 23 },
    ],
    totalInteractions: {
      value: 112,
      description: "Total interactions across all metrics",
    },
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: "auto" }}>
      <Typography variant="h4" gutterBottom>
        PDF Export Button Variants - Compact Design
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Redesigned PDF export buttons with compact layouts for better space utilization
      </Typography>

      <Grid container spacing={3}>
        {/* Chip Variant */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Chip Variant (Most Compact)
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Ultra-compact chip design - perfect for tight spaces
              </Typography>
              
              <Stack direction="row" spacing={2} alignItems="center">
                <PDFExportButton
                  reportTitle="Demo Report"
                  filterCriteria={mockFilterCriteria}
                  summaryData={mockSummaryData}
                  variant="chip"
                  size="small"
                />
                <PDFExportButton
                  reportTitle="Demo Report"
                  filterCriteria={mockFilterCriteria}
                  summaryData={mockSummaryData}
                  variant="chip"
                  size="medium"
                  color="primary"
                />
                <PDFExportButton
                  reportTitle="Demo Report"
                  filterCriteria={mockFilterCriteria}
                  summaryData={mockSummaryData}
                  variant="chip"
                  size="large"
                  color="secondary"
                />
              </Stack>
              
              <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                Height: 28px | Width: 60px min | Best for: Toolbars, compact layouts
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Compact Variant */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Compact Variant
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Small button design - good balance of size and visibility
              </Typography>
              
              <Stack direction="row" spacing={2} alignItems="center">
                <PDFExportButton
                  reportTitle="Demo Report"
                  filterCriteria={mockFilterCriteria}
                  summaryData={mockSummaryData}
                  variant="compact"
                  color="primary"
                />
                <PDFExportButton
                  reportTitle="Demo Report"
                  filterCriteria={mockFilterCriteria}
                  summaryData={mockSummaryData}
                  variant="compact"
                  color="secondary"
                />
                <PDFExportButton
                  reportTitle="Demo Report"
                  filterCriteria={mockFilterCriteria}
                  summaryData={mockSummaryData}
                  variant="compact"
                  color="error"
                />
              </Stack>
              
              <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                Height: 32px | Width: 70px min | Best for: Action bars, secondary actions
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Comparison with Original */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Size Comparison
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Compare the new compact variants with the original button design
              </Typography>
              
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={3}>
                  <Typography variant="subtitle2" gutterBottom>
                    Original (54px height)
                  </Typography>
                  <PDFExportButton
                    reportTitle="Demo Report"
                    filterCriteria={mockFilterCriteria}
                    summaryData={mockSummaryData}
                    variant="button"
                    buttonText="PDF"
                  />
                </Grid>
                
                <Grid item xs={12} sm={3}>
                  <Typography variant="subtitle2" gutterBottom>
                    Compact (32px height)
                  </Typography>
                  <PDFExportButton
                    reportTitle="Demo Report"
                    filterCriteria={mockFilterCriteria}
                    summaryData={mockSummaryData}
                    variant="compact"
                  />
                </Grid>
                
                <Grid item xs={12} sm={3}>
                  <Typography variant="subtitle2" gutterBottom>
                    Chip (28px height)
                  </Typography>
                  <PDFExportButton
                    reportTitle="Demo Report"
                    filterCriteria={mockFilterCriteria}
                    summaryData={mockSummaryData}
                    variant="chip"
                  />
                </Grid>
                
                <Grid item xs={12} sm={3}>
                  <Typography variant="subtitle2" gutterBottom>
                    Icon Only
                  </Typography>
                  <PDFExportButton
                    reportTitle="Demo Report"
                    filterCriteria={mockFilterCriteria}
                    summaryData={mockSummaryData}
                    variant="icon"
                    size="small"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Usage Examples */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Usage Examples
              </Typography>
              
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" gutterBottom>
                  In Filter Bars (Recommended: Chip or Compact)
                </Typography>
                <Box sx={{ 
                  display: "flex", 
                  gap: 1, 
                  p: 2, 
                  bgcolor: "grey.50", 
                  borderRadius: 1,
                  alignItems: "center"
                }}>
                  <Typography variant="body2">Apply</Typography>
                  <Divider orientation="vertical" flexItem />
                  <Typography variant="body2">Excel</Typography>
                  <Divider orientation="vertical" flexItem />
                  <PDFExportButton
                    reportTitle="Demo Report"
                    filterCriteria={mockFilterCriteria}
                    variant="chip"
                  />
                </Box>
              </Box>

              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  In Action Toolbars (Recommended: Compact)
                </Typography>
                <Box sx={{ 
                  display: "flex", 
                  gap: 2, 
                  p: 2, 
                  bgcolor: "grey.50", 
                  borderRadius: 1,
                  alignItems: "center"
                }}>
                  <Typography variant="body2">Actions:</Typography>
                  <PDFExportButton
                    reportTitle="Demo Report"
                    filterCriteria={mockFilterCriteria}
                    variant="compact"
                    color="primary"
                  />
                  <PDFExportButton
                    reportTitle="Demo Report"
                    filterCriteria={mockFilterCriteria}
                    variant="compact"
                    color="secondary"
                  />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default PDFExportButtonDemo;
