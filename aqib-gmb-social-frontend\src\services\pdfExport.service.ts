import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import { saveAs } from "file-saver";

export interface FilterCriteria {
  businessName?: string;
  accountName?: string;
  locationName?: string;
  fromDate?: string;
  toDate?: string;
  duration?: string;
  [key: string]: any;
}

export interface SummaryStatistic {
  label: string;
  value: number;
  icon?: string;
}

export interface SummaryData {
  title: string;
  statistics: SummaryStatistic[];
  totalInteractions?: {
    value: number;
    description: string;
  };
}

export interface ChartElement {
  element: HTMLElement;
  title: string;
  width?: number;
  height?: number;
}

export interface PDFExportOptions {
  reportTitle: string;
  filterCriteria: FilterCriteria;
  chartElements: ChartElement[];
  summaryData?: SummaryData;
  filename?: string;
  orientation?: "portrait" | "landscape";
  format?: "a4" | "letter";
  includeTimestamp?: boolean;
  includeHeader?: boolean;
  includeFooter?: boolean;
}

export class PDFExportService {
  private static readonly DEFAULT_OPTIONS = {
    orientation: "portrait" as const,
    format: "a4" as const,
    includeTimestamp: true,
    includeHeader: true,
    includeFooter: true,
  };

  private static readonly PAGE_MARGINS = {
    top: 20,
    bottom: 20,
    left: 20,
    right: 20,
  };

  private static readonly CHART_SPACING = 10;
  private static readonly HEADER_HEIGHT = 50; // Increased from 40 to accommodate larger logo
  private static readonly FOOTER_HEIGHT = 20;

  /**
   * Export analytics report to PDF
   */
  static async exportReportToPDF(options: PDFExportOptions): Promise<void> {
    try {
      const mergedOptions = { ...this.DEFAULT_OPTIONS, ...options };

      // Create PDF document
      const pdf = new jsPDF({
        orientation: mergedOptions.orientation,
        unit: "mm",
        format: mergedOptions.format,
      });

      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const contentWidth =
        pageWidth - this.PAGE_MARGINS.left - this.PAGE_MARGINS.right;
      const contentHeight =
        pageHeight - this.PAGE_MARGINS.top - this.PAGE_MARGINS.bottom;

      let currentY = this.PAGE_MARGINS.top;

      // Add header
      if (mergedOptions.includeHeader) {
        currentY = await this.addHeader(
          pdf,
          mergedOptions.reportTitle,
          currentY,
          pageWidth
        );
      }

      // Add filter criteria
      currentY = this.addFilterCriteria(
        pdf,
        mergedOptions.filterCriteria,
        currentY,
        contentWidth
      );

      // Add summary statistics if provided
      if (mergedOptions.summaryData) {
        currentY = this.addSummaryStatistics(
          pdf,
          mergedOptions.summaryData,
          currentY,
          contentWidth
        );
      }

      // Add timestamp
      if (mergedOptions.includeTimestamp) {
        currentY = this.addTimestamp(pdf, currentY, contentWidth);
      }

      // Add charts
      console.log(
        "📊 About to add charts to PDF:",
        mergedOptions.chartElements.map((ce) => ({
          title: ce.title,
          element: ce.element,
        }))
      );

      for (let i = 0; i < mergedOptions.chartElements.length; i++) {
        const chartElement = mergedOptions.chartElements[i];
        console.log(
          `📊 Adding chart ${i + 1}/${mergedOptions.chartElements.length}: ${
            chartElement.title
          }`
        );

        // Check if we need a new page
        const estimatedChartHeight = 80; // Estimate chart height in mm
        if (
          currentY + estimatedChartHeight >
          pageHeight - this.PAGE_MARGINS.bottom - this.FOOTER_HEIGHT
        ) {
          pdf.addPage();
          currentY = this.PAGE_MARGINS.top;
        }

        currentY = await this.addChartToPDF(
          pdf,
          chartElement,
          currentY,
          contentWidth,
          pageHeight - this.PAGE_MARGINS.bottom - this.FOOTER_HEIGHT
        );

        // Add spacing between charts
        if (i < mergedOptions.chartElements.length - 1) {
          currentY += this.CHART_SPACING;
        }
      }

      // Add footer
      if (mergedOptions.includeFooter) {
        this.addFooter(pdf, pageHeight);
      }

      // Generate filename
      const filename =
        mergedOptions.filename ||
        this.generateFilename(mergedOptions.reportTitle);

      // Save the PDF
      pdf.save(filename);
    } catch (error) {
      console.error("Error generating PDF report:", error);
      throw new Error(
        `Failed to generate PDF report: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Convert logo image to base64
   */
  private static async getLogoAsBase64(): Promise<string | null> {
    try {
      // Import the logo image
      const logoModule = await import("../assets/common/Logo.png");
      const logoUrl = logoModule.default;

      return new Promise((resolve) => {
        const img = new Image();
        img.crossOrigin = "anonymous";
        img.onload = () => {
          const canvas = document.createElement("canvas");
          const ctx = canvas.getContext("2d");
          canvas.width = img.width;
          canvas.height = img.height;
          ctx?.drawImage(img, 0, 0);
          const base64 = canvas.toDataURL("image/png");
          resolve(base64);
        };
        img.onerror = () => resolve(null);
        img.src = logoUrl;
      });
    } catch (error) {
      console.error("Error loading logo:", error);
      return null;
    }
  }

  /**
   * Add header to PDF with logo and title
   */
  private static async addHeader(
    pdf: jsPDF,
    title: string,
    currentY: number,
    pageWidth: number
  ): Promise<number> {
    try {
      // Load and add logo on the left
      const logoBase64 = await this.getLogoAsBase64();
      if (logoBase64) {
        const logoWidth = 60; // Logo width in mm (increased from 30)
        const logoHeight = 15; // Logo height in mm (increased from 15)
        pdf.addImage(
          logoBase64,
          "PNG",
          this.PAGE_MARGINS.left,
          currentY + 2,
          logoWidth,
          logoHeight
        );
      }

      // Add title on the right
      pdf.setFontSize(18);
      pdf.setFont("helvetica", "bold");
      const titleX = pageWidth - this.PAGE_MARGINS.right - 10; // Position from right
      pdf.text(title, titleX, currentY + 12, { align: "right" });

      // Add line under header
      pdf.setLineWidth(0.5);
      pdf.line(
        this.PAGE_MARGINS.left,
        currentY + 20,
        pageWidth - this.PAGE_MARGINS.right,
        currentY + 20
      );

      return currentY + this.HEADER_HEIGHT;
    } catch (error) {
      console.error("Error adding header:", error);
      // Fallback to text-only header
      pdf.setFontSize(18);
      pdf.setFont("helvetica", "bold");
      pdf.text(title, pageWidth / 2, currentY + 10, { align: "center" });

      pdf.setLineWidth(0.5);
      pdf.line(
        this.PAGE_MARGINS.left,
        currentY + 15,
        pageWidth - this.PAGE_MARGINS.right,
        currentY + 15
      );

      return currentY + this.HEADER_HEIGHT;
    }
  }

  /**
   * Add filter criteria to PDF
   */
  private static addFilterCriteria(
    pdf: jsPDF,
    criteria: FilterCriteria,
    currentY: number,
    contentWidth: number
  ): number {
    pdf.setFontSize(12);
    pdf.setFont("helvetica", "bold");
    pdf.text("Filter Criteria:", this.PAGE_MARGINS.left, currentY);
    currentY += 8;

    pdf.setFont("helvetica", "normal");
    pdf.setFontSize(10);

    const criteriaEntries = Object.entries(criteria).filter(
      ([_, value]) => value && value !== "0" && value !== ""
    );

    if (criteriaEntries.length === 0) {
      pdf.text("No filters applied", this.PAGE_MARGINS.left, currentY);
      currentY += 6;
    } else {
      for (const [key, value] of criteriaEntries) {
        const label = this.formatFilterLabel(key);

        // Special handling for location names - put each location on a new line
        if (key === "locationName" && typeof value === "string") {
          // Add the label first
          pdf.text(`${label}:`, this.PAGE_MARGINS.left, currentY);
          currentY += 6;

          // Split locations and handle each one separately
          // This is a simple approach - we'll need to improve this based on actual data structure
          const locations = this.splitLocationNames(value);

          for (const location of locations) {
            const maxWidth = contentWidth - 20; // Extra indent for location items
            const wrappedLines = this.wrapText(
              pdf,
              `• ${location.trim()}`,
              maxWidth
            );

            for (const line of wrappedLines) {
              pdf.text(line, this.PAGE_MARGINS.left + 10, currentY); // Indent location items
              currentY += 6;
            }
          }
        } else {
          // Normal handling for other fields
          const text = `${label}: ${value}`;
          const maxWidth = contentWidth - 10;
          const wrappedLines = this.wrapText(pdf, text, maxWidth);

          for (const line of wrappedLines) {
            pdf.text(line, this.PAGE_MARGINS.left, currentY);
            currentY += 6;
          }
        }
      }
    }

    return currentY + 5;
  }

  /**
   * Add summary statistics to PDF
   */
  private static addSummaryStatistics(
    pdf: jsPDF,
    summaryData: SummaryData,
    currentY: number,
    contentWidth: number
  ): number {
    // Add section title
    pdf.setFontSize(14);
    pdf.setFont("helvetica", "bold");
    pdf.text(summaryData.title, this.PAGE_MARGINS.left, currentY);
    currentY += 15;

    // Add statistics in a grid layout (3 columns)
    const colWidth = contentWidth / 3;
    const startX = this.PAGE_MARGINS.left;

    pdf.setFontSize(12);
    pdf.setFont("helvetica", "normal");

    for (let i = 0; i < summaryData.statistics.length; i++) {
      const stat = summaryData.statistics[i];
      const colIndex = i % 3;
      const x = startX + colIndex * colWidth;

      // If starting a new row, move Y position down
      if (colIndex === 0 && i > 0) {
        currentY += 25;
      }

      // Add statistic box
      const boxHeight = 20;
      const boxWidth = colWidth - 10; // Leave some margin between columns

      // Draw border
      pdf.setDrawColor(200, 200, 200);
      pdf.setLineWidth(0.5);
      pdf.rect(x, currentY - 15, boxWidth, boxHeight);

      // Add value (large, bold)
      pdf.setFontSize(16);
      pdf.setFont("helvetica", "bold");
      pdf.text(stat.value.toLocaleString(), x + 5, currentY - 5);

      // Add label (smaller, normal)
      pdf.setFontSize(10);
      pdf.setFont("helvetica", "normal");
      pdf.text(stat.label, x + 5, currentY + 2);
    }

    // Move to next row if we have items
    if (summaryData.statistics.length > 0) {
      currentY += 25;
    }

    // Add total interactions if provided
    if (summaryData.totalInteractions) {
      currentY += 10;

      // Center the total interactions section
      const totalBoxWidth = contentWidth * 0.6;
      const totalBoxX = startX + (contentWidth - totalBoxWidth) / 2;

      // Draw border for total
      pdf.setDrawColor(100, 100, 100);
      pdf.setLineWidth(1);
      pdf.rect(totalBoxX, currentY - 15, totalBoxWidth, 30);

      // Add total value
      pdf.setFontSize(20);
      pdf.setFont("helvetica", "bold");
      const totalText = summaryData.totalInteractions.value.toLocaleString();
      const totalTextWidth = pdf.getTextWidth(totalText);
      pdf.text(
        totalText,
        totalBoxX + (totalBoxWidth - totalTextWidth) / 2,
        currentY - 2
      );

      // Add description
      pdf.setFontSize(10);
      pdf.setFont("helvetica", "normal");
      const descText = summaryData.totalInteractions.description;
      const descTextWidth = pdf.getTextWidth(descText);
      pdf.text(
        descText,
        totalBoxX + (totalBoxWidth - descTextWidth) / 2,
        currentY + 8
      );

      currentY += 35;
    }

    return currentY + 10;
  }

  /**
   * Split location names intelligently
   * This is a heuristic approach since we don't have the original array structure
   */
  private static splitLocationNames(locationString: string): string[] {
    // If it doesn't contain commas, it's a single location
    if (!locationString.includes(",")) {
      return [locationString];
    }

    // Try to detect patterns that indicate separate locations
    // Look for patterns like "Location1, Location2" vs "Address, City, State"

    // Split by comma and try to group related parts
    const parts = locationString.split(",").map((part) => part.trim());
    const locations: string[] = [];
    let currentLocation = "";

    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];

      // If this part looks like it starts a new location (contains common business indicators)
      // or if the current location is getting very long, start a new location
      if (
        currentLocation &&
        (this.looksLikeNewLocation(part) || currentLocation.length > 100) // Arbitrary length threshold
      ) {
        locations.push(currentLocation);
        currentLocation = part;
      } else {
        currentLocation = currentLocation
          ? `${currentLocation}, ${part}`
          : part;
      }
    }

    // Add the last location
    if (currentLocation) {
      locations.push(currentLocation);
    }

    return locations.length > 0 ? locations : [locationString];
  }

  /**
   * Heuristic to detect if a part looks like the start of a new location
   */
  private static looksLikeNewLocation(part: string): boolean {
    // Common business name patterns
    const businessIndicators = [
      /^[A-Z][a-zA-Z\s]+ -/, // "BusinessName -"
      /^[A-Z][a-zA-Z\s]+ \(/, // "BusinessName ("
      /\b(Office|Store|Center|Centre|Clinic|Hospital|Restaurant|Shop)\b/i,
      /\b(Corporate|Main|Branch|Headquarters|HQ)\b/i,
    ];

    return businessIndicators.some((pattern) => pattern.test(part));
  }

  /**
   * Wrap text to fit within specified width
   */
  private static wrapText(
    pdf: jsPDF,
    text: string,
    maxWidth: number
  ): string[] {
    const words = text.split(" ");
    const lines: string[] = [];
    let currentLine = "";

    for (const word of words) {
      const testLine = currentLine ? `${currentLine} ${word}` : word;
      const textWidth = pdf.getTextWidth(testLine);

      if (textWidth <= maxWidth) {
        currentLine = testLine;
      } else {
        if (currentLine) {
          lines.push(currentLine);
          currentLine = word;
        } else {
          // Single word is too long, truncate it
          lines.push(this.truncateText(pdf, word, maxWidth));
          currentLine = "";
        }
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines;
  }

  /**
   * Truncate text to fit within specified width
   */
  private static truncateText(
    pdf: jsPDF,
    text: string,
    maxWidth: number
  ): string {
    if (pdf.getTextWidth(text) <= maxWidth) {
      return text;
    }

    let truncated = text;
    while (
      pdf.getTextWidth(truncated + "...") > maxWidth &&
      truncated.length > 0
    ) {
      truncated = truncated.slice(0, -1);
    }

    return truncated + "...";
  }

  /**
   * Add timestamp to PDF
   */
  private static addTimestamp(
    pdf: jsPDF,
    currentY: number,
    contentWidth: number
  ): number {
    const timestamp = new Date().toLocaleString();
    pdf.setFontSize(10);
    pdf.setFont("helvetica", "italic");
    pdf.text(`Generated on: ${timestamp}`, this.PAGE_MARGINS.left, currentY);

    return currentY + 15;
  }

  /**
   * Add chart to PDF
   */
  private static async addChartToPDF(
    pdf: jsPDF,
    chartElement: ChartElement,
    currentY: number,
    contentWidth: number,
    maxY: number
  ): Promise<number> {
    try {
      // Capture chart as image (no extra title needed - chart has its own)
      const canvas = await html2canvas(chartElement.element, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: "#ffffff",
        width: chartElement.width || chartElement.element.offsetWidth,
        height: chartElement.height || chartElement.element.offsetHeight,
      });

      // Convert canvas to image data
      const imgData = canvas.toDataURL("image/png");

      // Calculate image dimensions to fit within content width
      const imgWidth = contentWidth;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      // Check if image fits on current page
      if (currentY + imgHeight > maxY) {
        pdf.addPage();
        currentY = this.PAGE_MARGINS.top;
      }

      // Add image to PDF
      pdf.addImage(
        imgData,
        "PNG",
        this.PAGE_MARGINS.left,
        currentY,
        imgWidth,
        imgHeight
      );

      return currentY + imgHeight + 5;
    } catch (error) {
      console.error(
        `Error adding chart "${chartElement.title}" to PDF:`,
        error
      );

      // Add error message instead of chart
      pdf.setFontSize(10);
      pdf.setFont("helvetica", "italic");
      pdf.text(
        `Error loading chart: ${chartElement.title}`,
        this.PAGE_MARGINS.left,
        currentY
      );

      return currentY + 10;
    }
  }

  /**
   * Add footer to PDF
   */
  private static addFooter(pdf: jsPDF, pageHeight: number): void {
    const pageCount = pdf.getNumberOfPages();

    for (let i = 1; i <= pageCount; i++) {
      pdf.setPage(i);
      pdf.setFontSize(8);
      pdf.setFont("helvetica", "normal");

      // Add page number
      const pageText = `Page ${i} of ${pageCount}`;
      const pageWidth = pdf.internal.pageSize.getWidth();
      pdf.text(pageText, pageWidth - this.PAGE_MARGINS.right, pageHeight - 10, {
        align: "right",
      });

      // Add generation info
      pdf.text(
        "Generated by MyLocobiz Analytics",
        this.PAGE_MARGINS.left,
        pageHeight - 10
      );
    }
  }

  /**
   * Format filter label for display
   */
  private static formatFilterLabel(key: string): string {
    const labelMap: Record<string, string> = {
      businessName: "Business",
      accountName: "Account",
      locationName: "Location",
      fromDate: "From Date",
      toDate: "To Date",
      duration: "Duration",
      businessId: "Business",
      accountId: "Account",
      locationId: "Location",
    };

    return (
      labelMap[key] ||
      key.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase())
    );
  }

  /**
   * Generate filename for PDF
   */
  private static generateFilename(reportTitle: string): string {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");
    const sanitizedTitle = reportTitle.replace(/[^a-zA-Z0-9]/g, "_");
    return `${sanitizedTitle}_${timestamp}.pdf`;
  }

  /**
   * Get chart title from component name or default titles
   */
  private static getChartTitleFromComponent(element: HTMLElement): string {
    // Map of common chart component patterns to their titles
    const chartTitleMap: Record<string, string> = {
      ratingdistribution: "Rating Distribution",
      ratingsvsmonth: "Ratings vs Month",
      reviewsvsreplies: "Reviews vs Replies",
      reviewvolume: "Review Volume",
      platformbreakdown: "Platform Breakdown",
      websiteclicks: "Website Clicks",
      callclicks: "Call Clicks",
      directionrequests: "Direction Requests",
      messagingrequests: "Messaging Requests",
      bookingrequests: "Booking Requests",
      ratingtrends: "Rating Trends",
      responsetrends: "Response Trends",
      volumetrends: "Volume Trends",
      responsetime: "Response Time Analysis",
    };

    // Try to find chart type from class names or data attributes
    const classList = element.className.toLowerCase();
    for (const [key, title] of Object.entries(chartTitleMap)) {
      if (classList.includes(key)) {
        return title;
      }
    }

    // Check parent elements for component indicators
    let parent = element.parentElement;
    while (parent && parent !== document.body) {
      const parentClass = parent.className.toLowerCase();
      for (const [key, title] of Object.entries(chartTitleMap)) {
        if (parentClass.includes(key)) {
          return title;
        }
      }
      parent = parent.parentElement;
    }

    return "Chart";
  }

  /**
   * Capture multiple chart elements from a container
   */
  static async captureChartsFromContainer(
    containerSelector: string,
    chartSelectors: string[] = [
      ".MuiBox-root canvas",
      ".chart-container canvas",
      "canvas",
    ]
  ): Promise<ChartElement[]> {
    // Handle multiple container selectors separated by comma
    const containerSelectors = containerSelector
      .split(",")
      .map((s) => s.trim());
    const containers: HTMLElement[] = [];

    for (const selector of containerSelectors) {
      const container = document.querySelector(selector) as HTMLElement;
      if (container) {
        containers.push(container);
        console.log("🔍 PDF Export Debug - Container found:", selector);
      } else {
        console.warn(`⚠️ Container not found: ${selector}`);
      }
    }

    if (containers.length === 0) {
      throw new Error(
        `No containers found for selectors: ${containerSelector}`
      );
    }

    const chartElements: ChartElement[] = [];
    const processedCanvases = new Set<HTMLElement>(); // Track processed canvas elements
    const processedContainers = new Set<string>(); // Track processed containers by unique identifier

    // Find all chart elements from all containers
    for (const container of containers) {
      console.log("🔍 Processing container:", container);

      for (const selector of chartSelectors) {
        const elements = container.querySelectorAll(
          selector
        ) as NodeListOf<HTMLElement>;

        console.log(
          `🔍 Found ${elements.length} elements for selector: ${selector} in container`
        );

        for (const element of elements) {
          // Skip if we've already processed this canvas element
          if (processedCanvases.has(element)) {
            console.log("🚫 Skipping already processed canvas element");
            continue;
          }

          // Check if the canvas has actual content (not empty)
          const canvas = element as HTMLCanvasElement;
          const context = canvas.getContext("2d");

          let hasContent = true; // Default to true to be safe
          try {
            const imageData = context?.getImageData(
              0,
              0,
              canvas.width,
              canvas.height
            );
            // Check if canvas has any non-transparent pixels
            const hasPixels = imageData?.data.some((pixel, index) => {
              // Check alpha channel (every 4th value) and RGB values
              return index % 4 === 3 ? pixel > 0 : pixel > 0;
            });
            hasContent = hasPixels || false;

            console.log(
              `🔍 Canvas content check: ${canvas.width}x${canvas.height}, hasContent: ${hasContent}`
            );
          } catch (error) {
            console.log(
              "🔍 Canvas content check failed, assuming has content:",
              error
            );
            hasContent = true; // If we can't check, assume it has content
          }

          if (!hasContent) {
            console.log("🚫 Skipping empty canvas element");
            continue;
          }

          console.log("🔍 Processing chart element:", element);
          // Find the chart container (usually the parent with chart title)
          let chartContainer = element.closest(".MuiBox-root") as HTMLElement;

          // Check if this container has data attributes, if not, look for a parent container
          if (
            chartContainer &&
            !chartContainer.hasAttribute("data-chart-title")
          ) {
            // Look for a parent MuiBox-root that might have the data attributes
            let parentContainer = chartContainer.parentElement?.closest(
              ".MuiBox-root"
            ) as HTMLElement;
            if (
              parentContainer &&
              parentContainer.hasAttribute("data-chart-title")
            ) {
              chartContainer = parentContainer;
              console.log(
                "🔍 Found parent container with data attributes:",
                parentContainer
              );
            }
          }

          // If we can't find a MuiBox-root, try to find the Grid item container
          if (!chartContainer) {
            chartContainer =
              (element.closest(".MuiGrid-item") as HTMLElement) ||
              (element.parentElement as HTMLElement);
          }

          if (chartContainer) {
            // Check if we already have this container or if this canvas is already captured
            const isDuplicateContainer = chartElements.some(
              (ce) => ce.element === chartContainer
            );
            const isDuplicateCanvas = chartElements.some((ce) => {
              // Check if this canvas is already inside an existing chart container
              return ce.element.contains(element);
            });

            console.log(
              "🔍 Duplicate container check:",
              isDuplicateContainer,
              "Container:",
              chartContainer
            );
            console.log(
              "🔍 Duplicate canvas check:",
              isDuplicateCanvas,
              "Canvas:",
              element
            );
            console.log(
              "🔍 Existing chart elements:",
              chartElements.length,
              chartElements.map((ce) => ce.title)
            );

            // Try multiple strategies to find chart title
            let title = "Chart";

            // Create a unique identifier for this container
            const containerRect = chartContainer.getBoundingClientRect();
            const chartType =
              chartContainer.getAttribute("data-chart-type") || "unknown";
            const chartTitle =
              chartContainer.getAttribute("data-chart-title") || "unknown";
            const containerKey = `${chartType}-${chartTitle}-${Math.round(
              containerRect.left
            )}-${Math.round(containerRect.top)}`;

            console.log("🔍 Container key:", containerKey);
            console.log(
              "🔍 Already processed containers:",
              Array.from(processedContainers)
            );

            const isAlreadyProcessed = processedContainers.has(containerKey);

            if (
              !isDuplicateContainer &&
              !isDuplicateCanvas &&
              !isAlreadyProcessed
            ) {
              console.log("🔍 Debugging chart container:", chartContainer);
              console.log("🔍 Container classes:", chartContainer.className);
              console.log(
                "🔍 Container data attributes:",
                chartContainer.dataset
              );

              // Strategy 1: Check for data-chart-title attribute
              const chartTitleAttr =
                chartContainer.getAttribute("data-chart-title");
              console.log("🔍 data-chart-title attribute:", chartTitleAttr);

              if (chartTitleAttr) {
                title = chartTitleAttr;
                console.log("✅ Found title from data attribute:", title);
              } else {
                // Strategy 2: Look for h5, h6, or .chart-title within the container
                let titleElement = chartContainer.querySelector(
                  "h5, h6, .chart-title"
                ) as HTMLElement;
                console.log(
                  "🔍 Found h5/h6/.chart-title element:",
                  titleElement
                );

                // Strategy 3: Look for Typography with variant h6 or specific classes
                if (!titleElement) {
                  titleElement = chartContainer.querySelector(
                    '[class*="MuiTypography-h6"], .MuiTypography-h6'
                  ) as HTMLElement;
                  console.log(
                    "🔍 Found MuiTypography-h6 element:",
                    titleElement
                  );
                }

                // Strategy 4: Look for any Typography element that looks like a title (first one, bold, etc.)
                if (!titleElement) {
                  const typographyElements = chartContainer.querySelectorAll(
                    ".MuiTypography-root"
                  );
                  console.log(
                    "🔍 Found Typography elements:",
                    typographyElements.length
                  );

                  for (const typoEl of typographyElements) {
                    const text = typoEl.textContent?.trim();
                    console.log("🔍 Checking Typography text:", text);
                    // Look for elements that seem like titles (not empty, not just numbers, reasonable length)
                    if (
                      text &&
                      text.length > 3 &&
                      text.length < 50 &&
                      !text.match(/^\d+$/) &&
                      !text.includes("Total:") &&
                      !text.includes("Average:")
                    ) {
                      titleElement = typoEl as HTMLElement;
                      console.log("✅ Found suitable title element:", text);
                      break;
                    }
                  }
                }

                if (titleElement?.textContent) {
                  title = titleElement.textContent.trim();
                  console.log("✅ Found title from element:", title);
                } else {
                  // Fallback: Try to get title from component name patterns
                  title = this.getChartTitleFromComponent(chartContainer);
                  console.log("✅ Found title from component mapping:", title);
                }
              }

              console.log("🎯 Final chart title:", title);
              chartElements.push({
                element: chartContainer,
                title: title,
              });

              // Mark this canvas and container as processed
              processedCanvases.add(element);
              processedContainers.add(containerKey);
            } else {
              // Get title for logging purposes
              const chartTitleAttr =
                chartContainer.getAttribute("data-chart-title");
              if (chartTitleAttr) {
                title = chartTitleAttr;
              } else {
                const titleElement = chartContainer.querySelector(
                  ".chart-title"
                ) as HTMLElement;
                if (titleElement?.textContent) {
                  title = titleElement.textContent.trim();
                }
              }
              console.log("🚫 Skipping duplicate chart:", title);
            }
          }
        }
      }
    } // Close container loop

    console.log(
      "🎯 Final chart elements found:",
      chartElements.map((ce) => ({ title: ce.title, element: ce.element }))
    );
    return chartElements;
  }
}
